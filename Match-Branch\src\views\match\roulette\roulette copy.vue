<template>
    <div class="page-body flex-row">
        <!-- 新增round选择组件 -->
        <div class="left-panel">
            <!-- <div class="panel">
                <div class="panel-body">
                  <round></round>
                </div>
            </div> -->
            <ticket :matchId="matchId" :items="state.items" :selected-option="selectedOption"
                @clear="resetSelectedOption"></ticket>
        </div>
        <div class="right-panel">
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Sector</h3>
                </div>
                <div class="panel-body">
                    <div class="options">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'Sector')" v-text="item.optionName"
                            :key="key" :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :title="'X ' + item.odds"></div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Exact Number</h3>
                </div>
                <div class="panel-body exact-number flex-row">
                    <div class="options zero">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName == '0')"
                            v-text="item.optionName" :key="key"
                            :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :title="'X ' + item.odds"></div>
                    </div>
                    <div class="options number">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName != '0')"
                            v-text="item.optionName" :key="key"
                            :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :title="'X ' + item.odds"></div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Other</h3>
                </div>
                <div class="panel-body">
                    <div class="options dozens ">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'Dozens')" v-text="item.optionName"
                            :key="key"
                            :class="[item.style, { 'selected': selectedOption.optionId === item.id, 'disabled': currentSelectedAmount === 0 }]"  :title="'X ' + item.odds">
                        </div>
                    </div>
                    <div class="options other">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type?.startsWith('Other'))"
                            v-text="item.optionName" :key="key"
                            :class="[item.style, { 'selected': selectedOption.optionId === item.id, 'disabled': currentSelectedAmount === 0 }]"  :title="'X ' + item.odds">
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">

import matchOptions from './option';

import Ticket from '../../components/ticket.vue';
import Round from '../../components/round.vue';

import { useResettableReactive } from '@/hooks/useResettableReactive';

import { EMatch } from '@/typings/enum';
import { MatchOption, StakeOrder } from '@/typings/typings';




const matchId = ref(EMatch.LuckyRoulette);

const [state] = useResettableReactive<StakeOrder>({
    items: []
})

const [selectedOption, resetSelectedOption] = useResettableReactive({
    optionId: 0,
    optionName: '',
    odds: 0,
    stake: 0,
    type: ''
});


const onOptionClick = (e: MatchOption) => {
    // 如果点击的是已选中的选项，则取消选中
    if (selectedOption.optionId === e.id) {
        resetSelectedOption();
        return;
    }

    // 否则选中新选项
    selectedOption.optionId = e.id;
    selectedOption.odds = e.odds;
    selectedOption.optionName = e.optionName;
    selectedOption.type = e.type!;
};



</script>
