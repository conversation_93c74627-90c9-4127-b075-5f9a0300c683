{"root": ["./src/main.ts", "./src/api/match.ts", "./src/api/order.ts", "./src/api/typings.d.ts", "./src/api/user.ts", "./src/constants/index.ts", "./src/extension/moment.ts", "./src/extension/numeral.ts", "./src/hooks/useprinter.ts", "./src/hooks/userequest.ts", "./src/hooks/useresettablereactive.ts", "./src/hooks/usescanlistener.ts", "./src/hooks/usethermalprinter.ts", "./src/i18n/index.ts", "./src/router/index.ts", "./src/router/routes.ts", "./src/store/global.ts", "./src/store/index.ts", "./src/typings/enum.ts", "./src/typings/typings.ts", "./src/utils/dialog.ts", "./src/utils/http.ts", "./src/utils/index.ts", "./src/views/match/color/option.ts", "./src/views/match/race/option.ts", "./src/views/match/roulette/option.ts", "./src/app.vue", "./src/components/svgicon.vue", "./src/components/io/io-table/io-table.vue", "./src/components/io/io-upload/io-upload.vue", "./src/views/components/navbar.vue", "./src/views/components/round.vue", "./src/views/components/ticket.vue", "./src/views/dashboard/dashboard copy.vue", "./src/views/dashboard/dashboard.vue", "./src/views/dashboard/new.vue", "./src/views/dashboard/components/car.vue", "./src/views/history/history.vue", "./src/views/history/orderdialog.vue", "./src/views/layout/layout.vue", "./src/views/match/color/color.vue", "./src/views/match/race/race.vue", "./src/views/match/roulette/roulette.vue", "./src/views/passport/login.vue", "./src/views/printer/printer-dialog.vue", "./src/views/printer/printer.vue", "./src/views/result/result.vue", "./src/views/setting/setting.vue", "./src/views/ticket/ticket-dialog.vue", "./src/views/ticket/ticket.vue", "./typings/auto-import.d.ts", "./typings/components.d.ts", "./typings/global.d.ts", "./typings/vite-env.d.ts"], "errors": true, "version": "5.6.3"}