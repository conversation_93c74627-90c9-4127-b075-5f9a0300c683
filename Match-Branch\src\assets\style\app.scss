/********** css rest ***************/
html,
body,
#app {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 400;
  /* background-color: #f7f7f7; */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1em;
  line-height: 1.42857143;
  min-width: 400px;

  .el-message-box {
    --el-messagebox-width: 300px;
  }
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-weight: 400;
  font-size: 100%;
  color: inherit;
  margin: 0;
}

.full-width {
  width: 100%;
}

*,
::after,
::after {
  box-sizing: border-box;
}

::selection {
  /* background: #00D1B2;
  color: #fff; */
}

.full-page {
  width: 100%;
  height: 100vh;
}

a {
  background: transparent;
  outline: none;
  transition: color .3s ease;
}


.text-right {
  text-align: right
}

.text-bold {
  font-weight: bold;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-start {
  display: flex;
  justify-content: flex-start;
}

.justify-center {
  display: flex;
  justify-content: center;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}

.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}

.justify-around {
  display: flex;
  justify-content: space-around;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.align-start {
  display: flex;
  align-items: flex-start;
}

.align-center {
  display: flex;
  align-items: center;
}

.align-end {
  display: flex;
  align-items: flex-end;
}


.panel {
  position: relative;
  background: #ffffff;
  margin-bottom: 5px;
  /* width: 100%; */
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);

  .panel-header {

    color: #444;
    display: block;
    padding: 5px 10px;
    position: relative;
    border-bottom: 1px solid #f1f1f1;

    &.with-border {
      border-bottom: 1px solid #f1f1f1;
    }

    .panel-title {
      display: inline-block;
      font-size: 16px;
      margin: 0;
      line-height: 1;
    }
  }

  .panel-body {
    padding: 5px 10px;
  }

  .panel-footer {
    padding: 5px 10px;
    border-top: 1px solid #f1f1f1;
  }
}


.options {
  display: flex;
  flex-direction: row;

  .item {
    width: 60px;
    height: 30px;
    color: #fff;
    text-align: center;
    line-height: 30px;
    border-radius: 3px;
    background-color: #635d5d;
    font-size: 16px;
    margin-right: 10px;
    cursor: pointer;
    user-select: none;

    &.selected {
      position: relative;
      box-shadow: 0 0 0 2px #fff, 0 0 0 4px #409EFF;
      transform: scale(1.03);
      transition: all 0.2s ease;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }

      &::after {
        content: '✓';
        position: absolute;
        top: -7px;
        right: -7px;
        background-color: #409EFF;
        color: white;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        font-size: 10px;
        line-height: 15px;
        text-align: center;
      }
    }

    &.green {
      background-color: #1cd41c;
    }

    &.red {
      background-color: #9b1e24;
    }

    &.black {
      background-color: #000000;
    }

    &.gray {
      background-color: #635d5d;
    }
  }
}

.sector,
.exact-number,
.dozens,
.other {}

.forecast {
  gap: 10px;
  flex-wrap: wrap;
  width: 400px;

  .item {
    margin-right: 0 !important;
    margin-bottom: 0;
  }
}

.exact-number {
  .number {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    height: 148px;
  }

  .zero {
    .item {
      line-height: 148px;
      height: 148px;
    }
  }

  .item {
    width: 40px;
    height: 35px;
    margin-right: 2px;
    margin-bottom: 2px;
    font-size: 16px;
    line-height: 35px;
  }
}

.dozens {
  .item {
    width: 130px;
    margin-right: 10px;
  }
}

.other {
  margin-top: 5px;

  .item {
    width: 60px;
    margin-right: 10px;
  }
}


.page-body {
  gap: 10px;
}

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.left-panel {
  // max-width: 480px;
}

.box {
  background-color: #D3D6E0;
  border-radius: 5px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  padding: 10px;
}

