import { MatchOption } from "@/typings/typings";

const MatchOptions: MatchOption[] = [
    {
        "id": 2001,
        "matchId": 2000,
        "odds": 3.5,
        "optionName": "1",
        "type": "Winner",
        "style": "",
        "createTime": "2024-11-26T09:26:16+08:00"
    },
    {
        "id": 2002,
        "matchId": 2000,
        "odds": 4.5,
        "optionName": "2",
        "type": "Winner",
        "style": "",
        "createTime": "2024-11-26T09:26:33+08:00"
    },
    {
        "id": 2003,
        "matchId": 2000,
        "odds": 5,
        "optionName": "3",
        "type": "Winner",
        "style": "",
        "createTime": "2024-11-26T09:26:37+08:00"
    },
    {
        "id": 2004,
        "matchId": 2000,
        "odds": 6,
        "optionName": "4",
        "type": "Winner",
        "style": "",
        "createTime": "2024-11-26T09:26:40+08:00"
    },
    {
        "id": 2005,
        "matchId": 2000,
        "odds": 6,
        "optionName": "5",
        "type": "Winner",
        "style": "",
        "createTime": "2024-11-26T09:26:44+08:00"
    },
    {
        "id": 2006,
        "matchId": 2000,
        "odds": 7.2,
        "optionName": "6",
        "type": "Winner",
        "style": "",
        "createTime": "2024-11-26T09:26:49+08:00"
    },
    {
        "id": 2007,
        "matchId": 2000,
        "odds": 1.85,
        "optionName": "1",
        "type": "1st/2nd",
        "style": "",
        "createTime": "2024-11-26T09:26:59+08:00"
    },
    {
        "id": 2008,
        "matchId": 2000,
        "odds": 2.3,
        "optionName": "2",
        "type": "1st/2nd",
        "style": "",
        "createTime": "2024-11-26T09:27:01+08:00"
    },
    {
        "id": 2009,
        "matchId": 2000,
        "odds": 2.5,
        "optionName": "3",
        "type": "1st/2nd",
        "style": "",
        "createTime": "2024-11-26T09:27:02+08:00"
    },
    {
        "id": 2010,
        "matchId": 2000,
        "odds": 3,
        "optionName": "4",
        "type": "1st/2nd",
        "style": "",
        "createTime": "2024-11-26T09:27:03+08:00"
    },
    {
        "id": 2011,
        "matchId": 2000,
        "odds": 3,
        "optionName": "5",
        "type": "1st/2nd",
        "style": "",
        "createTime": "2024-11-26T09:27:04+08:00"
    },
    {
        "id": 2012,
        "matchId": 2000,
        "odds": 3.5,
        "optionName": "6",
        "type": "1st/2nd",
        "style": "",
        "createTime": "2024-11-26T09:27:05+08:00"
    },
    {
        "id": 2013,
        "matchId": 2000,
        "odds": 1.5,
        "optionName": "Low",
        "type": "Other/Low",
        "style": "",
        "createTime": "2024-11-26T09:27:19+08:00"
    },
    {
        "id": 2014,
        "matchId": 2000,
        "odds": 2.2,
        "optionName": "High",
        "type": "Other/High",
        "style": "",
        "createTime": "2024-11-26T09:27:20+08:00"
    },
    {
        "id": 2015,
        "matchId": 2000,
        "odds": 2,
        "optionName": "Even",
        "type": "Other/Even",
        "style": "",
        "createTime": "2024-11-26T09:27:21+08:00"
    },
    {
        "id": 2016,
        "matchId": 2000,
        "odds": 1.6,
        "optionName": "Odd",
        "type": "Other/Odd",
        "style": "",
        "createTime": "2024-11-26T09:27:22+08:00"
    },
    {
        "id": 2017,
        "matchId": 2000,
        "odds": 13,
        "optionName": "1x2",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:28:52+08:00"
    },
    {
        "id": 2018,
        "matchId": 2000,
        "odds": 15,
        "optionName": "1x3",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:28:53+08:00"
    },
    {
        "id": 2019,
        "matchId": 2000,
        "odds": 18,
        "optionName": "1x4",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:28:54+08:00"
    },
    {
        "id": 2020,
        "matchId": 2000,
        "odds": 18,
        "optionName": "1x5",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:28:55+08:00"
    },
    {
        "id": 2021,
        "matchId": 2000,
        "odds": 22,
        "optionName": "1x6",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:28:56+08:00"
    },
    {
        "id": 2022,
        "matchId": 2000,
        "odds": 14,
        "optionName": "2x1",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:28:57+08:00"
    },
    {
        "id": 2023,
        "matchId": 2000,
        "odds": 20,
        "optionName": "2x3",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:28:57+08:00"
    },
    {
        "id": 2024,
        "matchId": 2000,
        "odds": 23,
        "optionName": "2x4",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:28:59+08:00"
    },
    {
        "id": 2025,
        "matchId": 2000,
        "odds": 24,
        "optionName": "2x5",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:28:59+08:00"
    },
    {
        "id": 2026,
        "matchId": 2000,
        "odds": 29,
        "optionName": "2x6",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:29:00+08:00"
    },
    {
        "id": 2027,
        "matchId": 2000,
        "odds": 16,
        "optionName": "3x1",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:29:01+08:00"
    },
    {
        "id": 2028,
        "matchId": 2000,
        "odds": 21,
        "optionName": "3x2",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:29:02+08:00"
    },
    {
        "id": 2029,
        "matchId": 2000,
        "odds": 27,
        "optionName": "3x4",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:29:03+08:00"
    },
    {
        "id": 2030,
        "matchId": 2000,
        "odds": 27,
        "optionName": "3x5",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:29:04+08:00"
    },
    {
        "id": 2031,
        "matchId": 2000,
        "odds": 32,
        "optionName": "3x6",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:29:05+08:00"
    },
    {
        "id": 2032,
        "matchId": 2000,
        "odds": 20,
        "optionName": "4x1",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:29:07+08:00"
    },
    {
        "id": 2033,
        "matchId": 2000,
        "odds": 26,
        "optionName": "4x2",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:35+08:00"
    },
    {
        "id": 2034,
        "matchId": 2000,
        "odds": 28,
        "optionName": "4x3",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:36+08:00"
    },
    {
        "id": 2035,
        "matchId": 2000,
        "odds": 33,
        "optionName": "4x5",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:37+08:00"
    },
    {
        "id": 2036,
        "matchId": 2000,
        "odds": 39,
        "optionName": "4x6",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:38+08:00"
    },
    {
        "id": 2037,
        "matchId": 2000,
        "odds": 20,
        "optionName": "5x1",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:39+08:00"
    },
    {
        "id": 2038,
        "matchId": 2000,
        "odds": 26,
        "optionName": "5x2",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:40+08:00"
    },
    {
        "id": 2039,
        "matchId": 2000,
        "odds": 28,
        "optionName": "5x3",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:42+08:00"
    },
    {
        "id": 2040,
        "matchId": 2000,
        "odds": 33,
        "optionName": "5x4",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:43+08:00"
    },
    {
        "id": 2041,
        "matchId": 2000,
        "odds": 39,
        "optionName": "5x6",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:44+08:00"
    },
    {
        "id": 2042,
        "matchId": 2000,
        "odds": 25,
        "optionName": "6x1",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:45+08:00"
    },
    {
        "id": 2043,
        "matchId": 2000,
        "odds": 31,
        "optionName": "6x2",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:30:46+08:00"
    },
    {
        "id": 2044,
        "matchId": 2000,
        "odds": 35,
        "optionName": "6x3",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:31:33+08:00"
    },
    {
        "id": 2045,
        "matchId": 2000,
        "odds": 40,
        "optionName": "6x4",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:31:35+08:00"
    },
    {
        "id": 2046,
        "matchId": 2000,
        "odds": 42,
        "optionName": "6x5",
        "type": "Forecast",
        "style": "",
        "createTime": "2024-11-26T09:31:36+08:00"
    }
]
export default MatchOptions;