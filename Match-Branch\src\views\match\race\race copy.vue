<template>
    <div class="page-body flex-row">
        <div class="left-panel">
            <ticket :matchId="matchId" :items="state.items" :selected-option="selectedOption"
                @clear="resetSelectedOption"></ticket>
        </div>
        <div class="right-panel">
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Winner</h3>
                </div>
                <div class="panel-body">
                    <div class="options sector">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'Winner')" v-text="item.optionName"
                            :key="key" :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :title="'X ' + item.odds"></div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">1st or 2nd</h3>
                </div>
                <div class="panel-body">
                    <div class="options sector">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == '1st/2nd')"
                            v-text="item.optionName" :key="key"
                            :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :title="'X ' + item.odds"></div>
                    </div>
                </div>
            </div>

            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Forecast</h3>
                </div>
                <div class="panel-body flex-row">
                    <div class="options sector forecast">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'Forecast')"
                            v-text="item.optionName" :key="key"
                            :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :title="'X ' + item.odds"></div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Other</h3>
                </div>
                <div class="panel-body">
                    <div class="options other">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type?.startsWith('Other'))"
                            v-text="item.optionName" :key="key"
                            :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :title="'X ' + item.odds"></div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>


<script setup lang="ts">
import matchOptions from './option';

import Ticket from '../../components/ticket.vue';

import { useResettableReactive } from '@/hooks/useResettableReactive';
import { MatchOption, StakeOrder } from '@/typings/typings';
import { EMatch } from '@/typings/enum';

const matchId = ref(EMatch.VirtualRace);

const [state] = useResettableReactive<StakeOrder>({
    branchId: 0,
    branchName: 'TEST',
    cashier: '',
    roundId: '',
    round: 0,
    items: []
})

const [selectedOption, resetSelectedOption] = useResettableReactive({
    optionId: 0,
    optionName: '',
    odds: 0,
    stake: 0,
    type: ''
});

const onOptionClick = (e: MatchOption) => {
    // 如果点击的是已选中的选项，则取消选中
    if (selectedOption.optionId === e.id) {
        resetSelectedOption();
        return;
    }

    // 否则选中新选项
    selectedOption.optionId = e.id;
    selectedOption.odds = e.odds;
    selectedOption.optionName = e.optionName;
    selectedOption.type = e.type!;
}

</script>