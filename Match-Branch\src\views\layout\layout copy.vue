<template>
    <div class="full-page console-page">
        <div class="console-header">
            <div class="header-left">
                <ul class="header-menu">
                    <!-- <li class="item">
                        <router-link to="/dashboard" activeClass="active">Dashboard</router-link>
                    </li> -->
                    <li class="item">
                        <router-link to="/roulette" activeClass="active">Lucky Roulette</router-link>
                    </li>
                    <li class="item">
                        <router-link to="/race" activeClass="active">Racing Car</router-link>
                    </li>
                    <li class="item">
                        <router-link to="/color" activeClass="active">Color Lucky</router-link>
                    </li>
                    <li class="item">
                        <router-link to="/ticket" activeClass="active">Ticket</router-link>
                    </li>
                    <li class="item">
                        <router-link to="/result" activeClass="active">Results</router-link>
                    </li>
                </ul>
            
            </div>
            <div class="header-right">
                <div class="cashier-info">
                    <span>Welcome, {{ profileModel?.nickName }}</span>
                    <span>Branch: {{ profileModel?.branchName }}</span>
                </div>
                <div class="user-avatar">
                    <!-- <img src="@/assets/images/avatar.png" alt="Avatar"> -->
                </div>
            </div>
        </div>
        <div class="console-body">
            <router-view v-slot="{ Component }">
                <keep-alive>
                    <component :is="Component" :key="$route.fullPath" />
                </keep-alive>
            </router-view>
        </div>
        <div class="console-footer">
            <ul class="footer-menu">
                <li class="item">
                    <router-link to="/printer" activeClass="active">Printer Settings</router-link>
                </li>
                <li class="item">
                    <a @click="logout">Logout</a>
                </li>
            </ul>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useGlobalStore } from '@/store/global';

const { userLogout, profileModel } = useGlobalStore();


const logout = () => {
    ElMessageBox.confirm('You will lose your session after logging out. Continue?', 'Warning', { type: 'warning', confirmButtonText: 'Confirm' })
        .then(() => {
            userLogout();
        })
        .catch(_error => { });
}

</script>


<style scoped lang="scss">
.console-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    box-sizing: border-box;
    padding: 10px;
    background-color: #000;
}

.console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #303643;
    padding: 0 10px;
    color: white;
    height: 50px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.cashier-info {
    display: flex;
    flex-direction: column;
}

.cashier-info span {
    font-size: 14px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-menu {
    display: flex;
    gap: 20px;
    font-size: 16px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.header-menu .item {
    position: relative;
}

.header-menu .item a {
    display: block;
    text-decoration: none;
    color: #fff;
    transition: background-color 0.3s ease;
    line-height: 50px;
    height: 50px;
    padding: 0 10px;
    white-space: nowrap;

    &.active {
        background-color: #409eff !important;
    }

    &:hover {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
    }
}


.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f5f5f5;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.logo {
    width: 45px;
    height: 45px;
    background: #f5f5f5;
    border-radius: 50%;
}

.console-body {
    flex-grow: 1;
    background-color: #4D5057;
    overflow-y: auto;
    padding: 10px;
}

.console-footer {
    background-color: #282931;
    padding: 10px 20px;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.footer-menu {
    display: flex;
    justify-content: center;
    gap: 20px;
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 16px;
}

.footer-menu .item a {
    text-decoration: none;
    color: rgba(255, 255, 255, 0.67);
    transition: color 0.3s ease;
    padding: 8px 12px;
    border-radius: 4px;
}

.footer-menu .item a:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.footer-menu .item .active {
    color: #3273dc;
    font-weight: bold;
    background-color: rgba(255, 255, 255, 0.2);
}
</style>