<template>
    <el-dialog v-model="state.visible" :title="state.title" width="800" draggable @closed="onClosed" append-to-body
        :close-on-click-modal="false">
        <!-- <div v-loading="state.loading"> -->
        <el-form ref="form" label-width="100px" class="form" label-position="left">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="Match Name">
                        {{ state.formData.matchName }}
                        <!-- <el-input readonly v-model=""></el-input> -->
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="Round">
                        {{ state.formData.round }}
                        <!-- <el-input readonly v-model="state.formData.round"></el-input> -->
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="Stake">
                        {{ $numeral(state.formData.stake) }}
                        <!-- <el-input readonly v-model="state.formData.stake"></el-input> -->
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="Max Payout">
                        {{ $numeral(state.formData.maxPayout) }}

                        <!-- <el-input readonly v-model="state.formData.maxPayout"></el-input> -->
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="Cashier">
                        {{ state.formData.cashier }}

                        <!-- <el-input readonly v-model="state.formData.cashier"></el-input> -->
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="Order Time">
                        {{ $moment(state.formData.createTime) }}

                        <!-- <el-input readonly v-model="state.formData.createTime"></el-input> -->
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-table border height="200" size="small" v-loading="state.loading" :data="state.formData.selections">
            <el-table-column prop="id" type="index" label="ID" />

            <el-table-column prop="optionName" label="WINNER" width="200">
                <template #default="{ row }">
                    {{ row.optionName }} ({{ row.optionType }})
                </template>
            </el-table-column>
            <el-table-column prop="stake" label="STAKE">
                <template #default="{ row }">
                    {{ $numeral(row.stake) }}
                </template>
            </el-table-column>
            <el-table-column prop="odds" label="ODDS" />
            <el-table-column prop="payout" label="PAYOUT">
                <template #default="{ row }">
                    {{ $numeral(row.payout) }}
                </template>
            </el-table-column>

            <el-table-column prop="" label="RESULT">
                <template #default="{ row }">
                    <el-tag type="primary" v-if="row.status == 0">WAIT</el-tag>
                    <el-tag type="danger" v-if="row.status == 10">WIN</el-tag>
                    <el-tag type="info" v-if="row.status == 20">LOSE</el-tag>
                    <!-- <el-button plain type="danger" size="small" @click="Cancel(row)">Cancel</el-button> -->
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <div class="justify-between">
                <div class="left">
                    <el-button type="success" plain>Pay Order</el-button>
                    <el-button type="danger" plain>Cancel Order</el-button>

                </div>
                <div class="right">
                    <el-button type="primary" :loading="state.loading" @click="onRefresh">Refresh</el-button>

                    <el-button type="primary" :loading="state.loading" @click="onClosed">Close</el-button>

                </div>
            </div>
            <!-- <el-button type="success" plain
                v-if="(state.formData.status == 10 || state.formData.status == 11) && state.formData.payStatus == 0">Pay
                Order</el-button> -->

        </template>
        <!-- </div> -->
    </el-dialog>
</template>

<script lang="ts" setup>
import OrderService from '@/api/order';
import { useResettableReactive } from '@/hooks/useResettableReactive';


const [state, resetState] = useResettableReactive({
    visible: false,
    loading: false,
    title: 'Order Detail',
    formData: {
        id: 0,
        branchId: 0,
        branchName: '',
        cashier: '',
        orderNO: ' ',
        round: '',
        matchId: 0,
        matchName: '',
        expireTime: '',
        maxPayout: 0,
        stake: 0,
        status: 0,
        payStatus: 0,
        printTime: '',
        createTime: ''
    }
});



const open = (id: number) => {
    state.visible = true;
    state.loading = true;
    OrderService.query({ id }).then(res => {
        state.title = `Order Detail [${res.result.orderNO}]`
        state.formData = res.result;
    }).finally(() => {
        state.loading = false;
    });
};

const onRefresh = () => {
    state.loading = true;
    open(state.formData.id);
};

const onClosed = () => {
    state.visible = false;
    resetState();
};


defineExpose({
    open,
});
</script>
<style lang="scss" scoped>
.form {
    .el-form-item {
        margin-bottom: 5px;
    }
}
</style>