import http from '@/utils/http';
import { OrderModel, PaginatedList, Result } from './typings';

export default class OrderService {
    public static submit(data: {
        matchId: number,
        round?: string | number,
        items: Array<{
            stake: number;
            maxPayout: number;
            odds: number;
            optionId?: number;
            optionName: string;
            optionType?: string;
        }>
    }): Promise<Result<OrderModel>> {
        return http.post("/order/submit", data);
    }

    public static getPaginatedList(data: {
        pageIndex: number,
        pageSize: number,
        matchId?: number,
        round?: number,
        cashier?: string
    }): Promise<PaginatedList<OrderModel>> {
        return http.post("/order/fetch", data);
    }

    public static cancel(data: {
        id: string
    }) {
        return http.post("/order/cancel", data)
    }
    public static cash(data: {
        id: string
    }) {
        return http.post("/order/cash", data)
    }

    public static query(params: { id: string }): Promise<Result<OrderModel>> {
        return http.get("/order/query", { params })
    }

    public static getByOrderNo(params: { orderNo: string }): Promise<Result<OrderModel>> {
        return http.get("/order/getByOrderNO", { params })
    }

    public static print(data: OrderModel): Promise<Result<any>> {
        return http.post("/print", data, {baseURL: import.meta.env.VITE_PRINTER_API || "http://localhost:9999"})
    }
}