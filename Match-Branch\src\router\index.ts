import { createRouter, createWebHashHistory } from 'vue-router';

import Cookies from 'js-cookie';

import routes from './routes';

import { TokenKey } from '@/constants';

const router = createRouter({
    history: createWebHashHistory(),
    routes,
    scrollBehavior(_to, _from, _savedPosition) {
        return { top: 0 };
    }
});



router.beforeEach(async (to, _from, next) => {
    const token = Cookies.get(TokenKey);

    if (token && to.path == '/login') {
        next('/');
        return;
    }

    if (!token && to.path != '/login') {
        next('/login');
        return;
    }

    next();
});


// router.onError((error, to) => {
//     const errors = ['Failed to fetch dynamically imported module', 'Unable to preload CSS'];
//     if (errors.some((e) => error.message.includes(e))) {
//         window.location.href = to.fullPath;
//     }
// })

export default router;