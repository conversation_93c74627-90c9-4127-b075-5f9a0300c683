export interface MatchOption {
    id: number;
    matchId: number;
    odds: number;
    optionName: string;
    style?: string;
    type?: string;
    createTime?: string;
}


export interface StakeOrder {
    branchId?: number;
    branchName?: string;
    cashier?: string;
    round?: number | string;
    roundId?: string;
    matchId?: number;
    items: Array<{
        stake: number;
        payout: number;
        odds: number;
        optionId: number;
        optionName: string;
        type: string;
    }>
}