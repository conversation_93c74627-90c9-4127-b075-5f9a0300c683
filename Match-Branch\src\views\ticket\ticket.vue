<template>
    <div class="page-body flex-col">
        <div class="panel">
            <div class="panel-body">
                <el-form :inline="true" :model="formData" size="small" ref="searchForm">
                    <el-form-item label="Bar Code">
                        <el-input placeholder="" v-model="formData.orderNO" />
                    </el-form-item>
                    <el-form-item label="Match">
                        <el-input placeholder="" v-model="formData.matchId" />
                    </el-form-item>
                    <el-form-item label="Round">
                        <el-input placeholder="" v-model="formData.round" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="danger" plain @click="resetFormData">Clear</el-button>
                        <el-button type="primary" @click="search">Search</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="panel">
            <div class="panel-header with-border">
                <div class="panel-title">Ticket List</div>
            </div>
            <div class="panel-body" style="padding: 0;">
                <io-table height="500" :pagination="state.pagination" :data="state.rows" @pager-change="onPagerChange">
                    <el-table-column prop="" label="" width="135">
                        <template #default="{ row }">
                            <el-button plain type="primary" size="small" @click="viewItem(row)">Detail</el-button>
                            <el-button plain type="info" size="small" @click="onPrint(row)">Print</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderNo" label="BAR CODE" show-overflow-tooltip width="120" />
                    <el-table-column prop="matchName" label="MATCH NAME" />
                    <el-table-column prop="round" label="ROUND" />
                    <el-table-column prop="stake" label="STAKE">
                        <template #default="{ row }">
                            {{ $numeral(row.stake) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="maxPayout" label="MAX PAYOUT">
                        <template #default="{ row }">
                            {{ $numeral(row.maxPayout) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="Status">
                        <template #default="{ row }">
                            <span v-if="row.status == 0">Submitted</span>
                            <span v-if="row.status == 10">Win</span>
                            <span v-if="row.status == 20">Lose</span>
                            <span v-if="row.status == 30">Canceled</span>
                            <span v-if="row.status == 40">Paid</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="actualPayout" label="ACTUAL PAYOUT">
                        <template #default="{ row }">
                            <!-- {{ $numeral(row.maxPayout) }} -->
                        </template>
                    </el-table-column>
                    <el-table-column prop="cashier" label="CASHIER" />
                </io-table>
            </div>
        </div>
    </div>

    <ticket-dialog ref="orderDialog" />
</template>

<script lang="ts" setup>
import OrderService from '@/api/order';
import { useResettableReactive } from '@/hooks/useResettableReactive';


import TicketDialog from './ticket-dialog.vue';
import usePrinter from '@/hooks/usePrinter';
import { OrderModel } from '@/api/typings';


const onPagerChange = (pagination: any) => {
    Object.assign(state.pagination, pagination);
    Object.assign(formData, pagination);

    getPaginatedList();
}

//搜索条件
const [formData, resetFormData] = useResettableReactive<{
    pageIndex: number,
    pageSize: number,
    orderNO: string,
    matchId?: number,
    round?: number,
    cashier: string,
}>({
    pageIndex: 1,
    pageSize: 15,
    orderNO: '',
    matchId: undefined,
    round: undefined,
    cashier: ''
})

const state = reactive<{
    rows: OrderModel[];
    pagination?: any;
}>({
    rows: [],
    pagination: {
        pageIndex: 1,
        pageSize: 15,
        totalCount: 100,
        totalPages: 0,
    },
});

const search = () => {
    formData.pageIndex = 1;
    getPaginatedList();
}

const orderDialogRef = useTemplateRef<InstanceType<typeof TicketDialog>>('orderDialog');

const viewItem = (item: OrderModel) => {
    orderDialogRef.value?.open(item.id);
}

const getPaginatedList = () => {
    const params = {
        ...formData,
        matchId: formData.matchId ? Number(formData.matchId) : undefined,
        round: formData.round ? Number(formData.round) : undefined
    };

    OrderService.getPaginatedList(params).then(res => {
        console.log(res);
        state.rows = res.items;
        state.pagination.pageIndex = res.pageIndex;
        state.pagination.totalCount = res.totalCount;
        state.pagination.totalPages = res.totalPages;
        formData.pageIndex = res.pageIndex;
    })
}


const onPrint = (order: any) => {
    OrderService.query({ id: order.id }).then(res => {
        usePrinter(res.result);
    });
}

onMounted(() => {
    getPaginatedList();
})

</script>

<style scoped lang="scss">
.page-body {
    flex-direction: column;
}
</style>