<template>
    <div class="nav-header">
        <span> Hello Cashier</span> <b>(Branch-1)</b>
    </div>
    <div class="page-body flex-row">
        <div class="left-panel">
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Winner</h3>
                </div>
                <div class="panel-body">
                    <div class="options">
                        <div class="item" @click="onOptionClick(item)"
                            :class="{ 'active': item.id == selectedOption.optionId }"
                            v-for="item in matchOptions.filter(o => o.type == 'Winner')">
                            <span class="num" v-text="item.optionName" :key="item.id"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">1st or 2nd</h3>
                </div>
                <div class="panel-body">
                    <div class="options">
                        <div class="item" @click="onOptionClick(item)"
                            :class="{ 'active': item.id == selectedOption.optionId }"
                            v-for="item in matchOptions.filter(o => o.type == '1st/2nd')">
                            <span class="num" v-text="item.optionName" :key="item.id"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Winner</h3>
                </div>
                <div class="panel-body">
                    <div class="options">
                        <div class="item" @click="onOptionClick(item)"
                            :class="{ 'active': item.id == selectedOption.optionId }"
                            v-for="item in matchOptions.filter(o => o.type == 'Low/High/Even/Odd')">
                            <span class="num" v-text="item.optionName" :key="item.id"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Forecast</h3>
                </div>
                <div class="panel-body">
                    <div class="options" v-for="idx in 6">
                        <div class="item" @click="onOptionClick(item)"
                            :class="{ 'active': item.id == selectedOption.optionId }"
                            v-for="item in matchOptions.filter(o => o.type == 'Forecast').splice((idx - 1) * 5, 5)">
                            <span class="num" v-text="item.optionName" :key="item.id"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="right-panel">
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Selections</h3>
                </div>
                <div class="panel-body">
                    <table class="betting-table">
                        <thead>
                            <tr>
                                <th style="width: 15%;">ID</th>
                                <th style="width: 30%;">Stake</th>
                                <th style="width: 30%;">Winner</th>
                                <th style="width: 25%;">Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr v-for="(item, idx) in state.selections">
                                <td style="width: 15%;">{{ idx + 1 }}</td>
                                <td style="width: 30%;">
                                    <!-- <el-input-number :min="500" :max="10000" v-model="item.stake" :step="500"></el-input-number> -->
                                    {{  $numeral(item.stake)}}
                                </td>
                                <td style="width: 30%;">{{ item.optionName }}
                                    <span class="type">({{ getTypeText(item.type) }})</span>
                                </td>
                                <td style="width: 25%;">
                                    <SvgIcon name="close" className="icon-close"></SvgIcon>
                                </td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td>Stake</td>
                                <td class="text-right" colspan="3">TZS {{ $numeral(stake) }}</td>
                            </tr>
                            <tr>
                                <td>Max Payout</td>
                                <td class="text-right" colspan="3">TZS {{ $numeral(maxPayout) }}</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="panel-footer">
                    <div class="options chip">
                        <div class="item" @click="onChipClick(500)">500</div>
                        <div class="item" @click="onChipClick(1000)">1,000</div>
                        <div class="item" @click="onChipClick(1500)">1,500</div>
                        <div class="item" @click="onChipClick(5000)">5,000</div>
                    </div>
                </div>
            </div>

            <div class="panel">
                <div class="panel-body justify-between">
                    <el-button style="width: 150px;" plain type="danger" size="large"
                        @click="clearSelections">Clear</el-button>
                    <el-button style="width: 150px;" type="primary" size="large" @click="submit">Confirm</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.page-body {
    max-width: 1000px;

    .right-panel {
        margin-left: 20px;
        max-width: 400px;
    }
}



.options {
    display: flex;
    margin-bottom: 8px;
    justify-content: flex-start;
    gap: 20px;

    &.chip {
        .item {
            width: auto;
            border-radius: 1000px;
            width: 80px;
            height: 80px;
            line-height: 80px;
            padding: 0;
            font-weight: bold;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.25);
            font-size: 18px;
            background-color: #d0daed;

        }
    }

    .item {
        width: 50px;
        border: 1px solid #f5f5f5;
        padding: 5px;
        text-align: center;
        border-radius: 6px;
        color: #1d1c1c;
        font-weight: 500;
        background-color: #eef3fc;
        cursor: pointer;
        user-select: none;
        align-items: center;
        &.active {
            background-color: #d8e4f8;
            border-color: transparent;
            color: #2160c4;
            font-weight: bold
        }
    }
}




.betting-table {
    border-radius: 5px;
    border-spacing: 0;
    width: 100%;
    white-space: nowrap;
    display: block;
    color: #3b3c3d;
    overflow: hidden;
    border: 1px solid #f5f5f5;

    tr {
        height: 35px;
    }

    td,
    th {
        padding: 3px 8px;
    }

    thead {
        height: 50px;
        background-color: #d8e4f8;

        th {
            font-weight: bold;
            text-align: center;

        }
    }

    thead,
    tbody tr,
    tfoot tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        text-align: left;
    }

    tbody {
        display: block;
        height: 354px;
        overflow-y: auto;

        tr {
            border-bottom: 1px solid #f5f4f1;

            td {
                text-align: center;

            }

            .type {
                display: block;
                font-size: 14px;
                color: #999;
                text-transform: uppercase;
            }
        }
    }

    tfoot {
        background-color: #d8e4f8;
        height: 70px;
    }
}
</style>

<script setup lang="ts">
import OrderService from '@/api/order';
import matchOptions, { MatchOption } from '@/constants/matchOptions';


console.log(matchOptions);

import { useResettableReactive } from '@/hooks/useResettableReactive';
import useThermalPrinter from '@/hooks/useThermalPrinter';


interface StakeOrder {
    branchId: number;
    branchName: string;
    cashier: string;
    round: string;
    matchId: number;
    selections: {
        stake: number;
        payout: number;
        odds: number;
        optionId: number;
        optionName: string;
        type: string;
    }[]
}


const [state, resetState] = useResettableReactive<StakeOrder>({
    branchId: 0,
    branchName: 'TEST',
    cashier: '',
    round: '',
    matchId: 1003,
    selections: []
})

const [selectedOption, resetSelectedOption] = useResettableReactive({
    optionId: 0,
    optionName: '',
    odds: 0,
    stake: 0,
    type: ''
});


const onOptionClick = (e: MatchOption) => {
    selectedOption.optionId = e.id;
    selectedOption.odds = e.odds;
    selectedOption.optionName = e.optionName;
    selectedOption.type = e.type!;
}

const clearSelections = () => {
    state.selections = [];
}

const getTypeText = (type: string) => {

    if (type == 'LOW_HIGH_EVEN_ODD') {
        return 'WINNER';
    }
    if (type == '1ST_OR_2ND') {
        return '1st or 2nd';
    }
    return type;
}

const onChipClick = (value: number) => {
    if (selectedOption.optionId == 0) {
        return;
    }
    selectedOption.stake = value;

    let item = state.selections.find(x => x.optionId == selectedOption.optionId);
    if (item != null) {
        item.stake += value;
        item.payout = item.stake * item.odds;
    } else {
        state.selections.push({
            stake: value,
            optionId: selectedOption.optionId,
            optionName: selectedOption.optionName,
            odds: selectedOption.odds,
            type: selectedOption.type,
            payout: value * selectedOption.odds,
        });
    }
    resetSelectedOption();
}


const submit = () => {
    if (state.selections.length == 0) {
        return;
    }


    OrderService.submit(state).then(res => {
        ElMessage({
            message: 'Congrats, this is a success message.',
            type: 'success',
            plain: true,
        })
        clearSelections();

        const { result } = res;

        let text2 = `[C]<u><font size='big'>MAGIC ORDER</font></u>
[L]
[L]<b>Branch: </b>${result.branchName}
[L]<b>Cashier: </b>${result.cashier}
[L]<b>Barcode: </b>${result.orderNO}
[L]<b>Date: </b>${new Date().valueOf()}
[C]================================================`;
        result.selections.forEach(s => {
            text2 += `
[L]Virtual Race[R]Round: ${result.round}
[L]<b>WIN: ${s.optionName} (1st)</b>[R]Odds:  ${s.odds}
[R]Stake: ${s.stake}  Payout: ${s.payout}
[C]------------------------------------------------`});
        text2 += `
[L][L]<b>Stake</b>[R]<b>TZS ${result.stake}</b>
[L][L]<b>Max Payout</b>[R]<b>TZS  ${result.maxPayout}</b>
[C]================================================
[L]
[C]<barcode type='128' height='10'>${result.orderNO}</barcode>
[L]
[L]Bet responsibly
[L]
[L]
[L]
[L]
[L]
[L]
`;
        console.log(text2);
        // return;
        const text3 = "[C]<u><font size='big'>MAGIC ORDER</font></u>\n" +
            "[L]\n" +
            `[L]<b>Branch: </b>${result.branchName}\n` +
            "[L]<b>Cashier: </b>TEST-Cashier\n" +
            "[L]<b>Barcode: </b>202412161535001\n" +
            "[L]<b>Date: </b>16-12-2024 15:35\n" +
            "[C]================================================\n" +
            "[L]Virtual Race[R]Round: 0001\n" +
            "[L]<b>WIN: 1 (1st)</b>[R]Odds: 3.5\n" +
            "[R]Stake: 1,000  Payout: 3,500\n" +
            "[L]\n" +
            "[C]------------------------------------------------\n" +
            "[L]Virtual Race<[R]Round: 0001\n" +
            "[L]<b>WIN: 2 (1st)</b>[R]Odds: 4.5\n" +
            "[R]Stake: 1,000  Payout: 4,500\n" +
            "[L]\n" +
            "[C]------------------------------------------------\n" +
            "[L]<b>Stake</b>[R]<b>TZS 2,000</b>\n" +
            "[L]<b>Max Payout</b>[R]<b>TZS 8,000</b>\n" +
            "[L]\n" +
            "[C]================================================\n" +
            "[L]\n" +
            "[C]<barcode type='128' height='15'>202412161535001</barcode>\n" +
            "[L]\n" +
            "[L]Bet responsibly" +
            "[L]\n" +
            "[L]\n" +
            "[L]\n" +
            "[L]\n" +
            "[L]\n" +
            "[L]\n";

        const data = {
            type: "bluetooth", // "bluetooth" | "tcp" | "usb"
            text: text2,
            id: 'Printer001',//'DC:0D:30:20:61:60',
            printerWidthMM: 72, //纸张宽度 （mm）
            // mmFeedPaper: 120,    //末端毫米距离进纸
            dotsFeedPaper: 0,      //末端进纸的距离
            printerNbrCharactersPerLine: 48  //每行字符数        80MM的最大48字符

        };

        ThermalPrinter.printFormattedTextAndCut(data, res => {
            console.log(res);
        }, error => {
            console.error(error);
        });
    });
}




const stake = computed(() => {
    return state.selections.reduce((acc, cur) => acc + cur.stake, 0);
})
const maxPayout = computed(() => {
    return state.selections.reduce((acc, cur) => acc + cur.payout, 0);
})


</script>
