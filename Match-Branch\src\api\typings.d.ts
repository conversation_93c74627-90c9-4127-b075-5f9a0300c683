

export interface TokenModel {
    token: string;
    expireAt: string;
}

export interface RoundModel {
    createTime: string;
    endTime: string;
    id: string;
    matchId: number;
    round: number;
    startTime: string;
    endTime: string;
}


export interface BranchModel {
    id: number;
    branchName: string;
    address: string;
    contactName: string;
    status: number;
    endTime?: string;
    createTime: string;
}

export interface ProfileModel {
    id: number;
    branchId: number;
    branchName?: string;
    nickName: string;
    loginAccount: string;
    loginPassword?: string;
    salt?: string;
    loginAt: string;
    lastLoginIp: string;
    createTime: string;
    token: string;
    expireAt: string;
}

export interface OrderModel {
    id: string;
    title?: string;
    branchId: number;
    branchName: string;
    cashier: string;
    orderNo: string;
    round: string;
    matchId: number;
    matchName: string;
    expireTime: string;
    maxPayout: number;
    actualPayout?: number;
    stake: number;
    status: number;
    payTime?: string;
    printTime: string;
    cancelTime?: string;
    createTime: string;
    items: Array<OrderItem>
}

export interface OrderItem {
    id: number;
    orderId: number;
    optionId: number;
    optionName: string;
    optionType: string;
    odds: number;
    maxPayout: number;
    actualPayout: number;
    stake: number;
    type: string;
    status: number;
    createTime: string;
}


type PrinterModel = {
    id?: string | number,
    name?: string;
    address: string;
    type: "bluetooth" | "tcp" | "usb" | "";
    port: number;
    printerWidthMM: 72 | 48;
    printerNbrCharactersPerLine: 48 | 32;
    paperWidth?: 72 | 48
}



interface PaginationParams {
    pageIndex: number;
    pageSize: number;
}


interface Result<T> {
    result: T;
    message: string;
    code: number;
}

interface PaginatedList<T> {
    pageIndex: number;
    totalCount: number;
    totalPages: number;
    items: T[];
}
