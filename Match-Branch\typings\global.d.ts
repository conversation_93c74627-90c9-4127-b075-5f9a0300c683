export { }

import { ComponentCustomProperties } from "@vue/runtime-core";

declare module '*.vue' {
    import { ComponentOptions } from 'vue'
    const componentOptions: ComponentOptions
    export default componentOptions
}

declare module 'vue' {
    export interface ComponentCustomProperties {
        $numeral(value: any, format: string = '0,0'): string;
        $moment(value: any, format: string = 'DD-MM-YYYY HH:mm'): string;
    }
}

declare global {
    interface Window {
        readonly ThermalPrinter: {
            printFormattedTextAndCut(data: {
                type: string;
                id: string | number;
                address?: string;
                port?: number;
                text: string;
                mmFeedPaper?: number;
                dotsFeedPaper?: number;
            }, success: (value?: string) => void, error: (value: { error?: string }) => void);
            listPrinters: (
                data: { type: 'bluetooth' | 'usb' | 'tcp' },
                success: (value: any) => void,
                error: (value: { error?: string }) => void
            ) => void;
        }
    }
}
