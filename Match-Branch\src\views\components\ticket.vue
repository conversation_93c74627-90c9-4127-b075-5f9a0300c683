<template>
    <div class="ticket-panel">
        <div class="panel-body">
            <table class="ticket-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">SN</th>
                        <th style="width: 100px;text-align: center;">Stake</th>
                        <th style="width: 80px;text-align: center;">Odds</th>
                        <th style="min-width: 190px;">Win</th>
                        <th style="width: 50px;"></th>
                    </tr>
                </thead>
                <el-scrollbar height="200px">
                    <tbody>
                        <tr v-for="(item, idx) in items">
                            <td style="width: 50px;">{{ idx + 1 }}</td>
                            <td style="width: 100px;text-align: right;">{{ $numeral(item.stake) }}</td>
                            <td style="width: 80px;">{{ item.odds }}</td>
                            <td style="min-width: 190px;" class="text-bold">{{ item.optionName }}</td>
                            <td style="width: 50px;">
                                <SvgIcon name="close" class="icon-close" @click="deleteSelection(item, idx)">
                                </SvgIcon>
                            </td>
                        </tr>
                    </tbody>
                </el-scrollbar>
                <tfoot>
                    <tr>
                        <td>Stake</td>
                        <td class="text-right text-bold" colspan="4">Tzs {{ $numeral(stake) }}</td>
                    </tr>
                    <tr>
                        <td>Max Payout</td>
                        <td class="text-right text-bold" colspan="4">Tzs {{ $numeral(maxPayout) }}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        <div class="panel-footer">
            <div class="stake">
                <button class="item" v-for="item in allowStakes" :disabled="isStakeButtonActive"
                    @click="onStakeClick(item)">
                    {{ $numeral(item) }}
                </button>

                <button class="item custom" :disabled="isStakeButtonActive" @click="openCustomStakeDialog">Custom</button>
            </div>
        </div>

        <el-dialog v-model="showCustomStakeDialog" title="Custom Stake" width="300" align-center append-to-body
            :show-close="false">
            <el-input-number :min="500" :max="100000" style="width: 100%;" v-model="customStake" :step="500" size="large"></el-input-number>
            <template #footer>
                <div class="justify-between">
                    <el-button type="danger" @click="closeCustomStakeDialog" plain>Cancel</el-button>
                    <el-button type="primary" @click="onCustomStakeConfirm">Confirm</el-button>
                </div>
            </template>
        </el-dialog>
    </div>

    <div class="panel">
        <div class="panel-body justify-between">
            <el-button style="width: 120px;" plain type="danger" :disabled="items.length == 0"
                @click="clearSelections">Clear
                Ticket</el-button>
            <el-button style="width: 120px;" type="primary" @click="submit" :disabled="items.length == 0">Print
                Ticket</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import OrderService from '@/api/order';
import usePrinter from '@/hooks/usePrinter';

import { useGlobalStore } from '@/store/global';
import { EMatch } from '@/typings/enum';
import { MatchOption } from '@/typings/typings';
import Dialog from '@/utils/dialog';
import { isBoolean } from 'lodash';


const allowStakes = [500, 1000, 2000, 5000];


const props = withDefaults(defineProps<{
    matchId: number,
    items: Array<{
        stake: number;
        payout: number;
        odds: number;
        optionId: number;
        optionName: string;
        type: string;
    }>;
    roundId?: string;
    selectedOption: {
        stake: number;
        payout?: number;
        odds: number;
        optionId: number;
        optionName: string;
        type: string;
    };
    disabled?: boolean;
    digits?: Array<MatchOption>;
}>(), {
    disabled: undefined,
    digits: () => []
});

const emits = defineEmits(['clear']);

// 判断金额按钮是否激活
const isStakeButtonActive = computed(() => {
    // 如果传入了 disabled 属性，则根据传入值判断
    if (isBoolean(props.disabled)) {
        return !props.disabled;
    }
    // 默认根据 optionId 判断
    return false; props.selectedOption.optionId == 0;
});


const deleteSelection = (_item: any, idx: number) => {
    props.items.splice(idx, 1);
}

const clearSelections = () => {
    props.items.length = 0;
}


const showCustomStakeDialog = ref(false);
const customStake = ref(500);
const openCustomStakeDialog = () => {
    showCustomStakeDialog.value = true;
}
const closeCustomStakeDialog = () => {
    customStake.value = 500;
    showCustomStakeDialog.value = false;
}
const onCustomStakeConfirm = () => {
    onStakeClick(customStake.value);
    closeCustomStakeDialog();
}

const submit = () => {
    if (props.items.length == 0) {
        return;
    }

    const globalStore = useGlobalStore();

    if (window.ThermalPrinter && !globalStore.printerModel?.id) {
        return Dialog.warning('Please select a printer first.');
    }

    const formData = {
        matchId: props.matchId,
        round: props.roundId ?? '',
        items: props.items.map(item => ({
            stake: item.stake,
            maxPayout: item.payout,
            optionId: item.optionId,
            optionName: item.optionName,
            odds: item.odds,
            optionType: item.type
        }))
    };

    OrderService.submit(formData).then(res => {
        ElMessage({
            message: 'Order confirmed, please wait for printing.',
            type: 'success',
            plain: true,
        })
        clearSelections();
        usePrinter(res.result);
    }).finally(() => {
        emits('clear');
    });
}


const digitsPayout = [0, 0, 0, 70, 175, 275, 2250, 6000, 50000];
const onDigitsClick = (value: number) => {
    if (props.digits.length == 0) {
        return;
    }

    const optionName = props.digits.map(item => item.optionName).join(',');
    let item = props.items.find(x => x.optionName == optionName);
    if (item != null) {
        item.stake += value;
        item.payout = item.stake * item.odds;
    } else {
        props.items.push({
            stake: value,
            optionId: 0,
            optionName: optionName,
            type: 'NUMBER',
            odds: digitsPayout[props.digits.length],
            payout: value * digitsPayout[props.digits.length],
        });
    }

    emits('clear');
}


const onStakeClick = (value: number) => {

    if (props.matchId == EMatch.ColorLucky && props.selectedOption.optionId == 0) {
        return onDigitsClick(value);
    }

    if (props.selectedOption.optionId == 0) {
        return;
    }
    props.selectedOption.stake = value;

    let item = props.items.find(x => x.optionId == props.selectedOption.optionId);

    if (item != null) {
        item.stake += value;
        item.payout = item.stake * item.odds;
    } else {
        props.items.push({
            stake: value,
            optionId: props.selectedOption.optionId,
            optionName: props.selectedOption.optionName,
            odds: props.selectedOption.odds,
            type: props.selectedOption.type,
            payout: value * props.selectedOption.odds,
        });
    }
    emits('clear');
}

const stake = computed(() => {
    return props.items.reduce((acc, cur) => acc + cur.stake, 0);
})
const maxPayout = computed(() => {
    return props.items.reduce((acc, cur) => acc + cur.payout, 0);
})

</script>

<style scoped lang="scss">
.ticket-panel{
    width: 400px;
    border-radius: 5px;
    padding: 5px;
    background-color: #A6ACE0;
}
.icon-close{
    width: 1.5em;
    height: 1.5em;
}

.stake {
    display: flex;
    justify-content: flex-start;
    gap: 10px 20px;
    flex-wrap: wrap;
    
    .item {
        border-radius: 1000px;
        width: 60px;
        height: 60px;
        line-height: 60px;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 1px 1px rgba(54, 53, 53, 0.5);
        font-size: 16px;
        background-color: #dbd8d8;
        transition: all 0.3s ease;
        border: 1px solid #dbd8d8;
        user-select: none;
        padding: 0;

        &:hover:not(:disabled) {
            cursor: pointer;
            transform: scale(1.05);
            background-color: #67c23a;
            color: white;
            box-shadow: 0 2px 8px rgba(103, 194, 58, 0.5);
        }

        &:active {
            cursor: pointer;
        }

        &[disabled] {
            opacity: 0.6;
            cursor: not-allowed;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.1);
                border-radius: 1000px;
            }
        }
    }
}

.ticket-table {
    // min-width: 460px;
    border-radius: 5px;
    border-spacing: 0;
    white-space: nowrap;
    display: block;
    color: #fff;
    overflow: hidden;
    background-color: #A6ACE0;
    border: 1px solid #f5f5f5;
    font-size: 16px;

    tr {
        height: 20px;
    }

    td,
    th {
        padding: 3px;
        box-sizing: border-box;
    }

    thead {
        height: 20px;
        background-color: #0D090A;

        th {
            font-weight: normal;
            text-align: center;

        }
    }

    thead,
    tbody tr,
    tfoot tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        text-align: left;
    }

    tbody {
        display: block;

        tr {
            border-bottom: 1px solid #f5f4f1;

            td {
                text-align: center;
                border-right: 1px solid #f5f5f5;

                &:last-child {
                    border-right: none;
                }
            }

            .type {
                display: block;
                font-size: 14px;
                color: #999;
                text-transform: uppercase;
            }
        }
    }

    tfoot {
        background-color: #0D090A;
    }
}
</style>
