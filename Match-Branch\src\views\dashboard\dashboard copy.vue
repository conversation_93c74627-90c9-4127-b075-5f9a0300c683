<template>
    <div class="container">
        <div class="left-panel">
            <div class="branch">
                <h3>Branch-1</h3>
            </div>
            <div class="channel">
                <div>
                    RACING
                </div>
                <div>
                    SPIN
                </div>
                <div>
                    COLOR
                </div>
            </div>
            <table class="betting-table">
                <thead>
                    <tr>
                        <th style="width: 15%;">ID</th>
                        <th style="width: 30%;">Stake</th>
                        <th style="width: 30%;">Winner</th>
                        <th style="width: 25%;">Action</th>
                    </tr>
                </thead>

                <tbody>
                    <tr v-for="(item, idx) in state.selections">
                        <td style="width: 15%;">{{ idx + 1 }}</td>
                        <td style="width: 30%;">{{ item.stake }}</td>
                        <td style="width: 30%;">{{ item.optionName }}</td>
                        <td style="width: 25%;">X</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td>Stake</td>
                        <td class="text-right" colspan="3">TZS {{ stake }}</td>
                    </tr>
                    <tr>
                        <td>Max Payout</td>
                        <td class="text-right" colspan="3">TZS {{ maxPayout }}</td>
                    </tr>
                </tfoot>
            </table>
            <div class="action-buttons">
                <button class="btn btn-primary" @click="submit">
                    Confirm
                </button>
                <button class="btn btn-danger" @click="clearSelections">
                    Clear
                </button>

                <!-- <button class="btn btn-info">
                    Print
                </button> -->
            </div>
            <div class="chip">
                <div class="item" @click="onChipClick(500)">500</div>
                <div class="item" @click="onChipClick(1000)">1000</div>
                <div class="item" @click="onChipClick(1500)">1500</div>
                <div class="item manual">输入</div>
            </div>
        </div>
        <div class="right-panel">

            <div class="tabs">
                <div class="tab-header">
                    <div class="tab-item">Order</div>
                    <div class="tab-item">History</div>
                </div>
                <div class="tab-panel">

                    <div class="betting-box">
                        <h3 class="title">Winner</h3>
                        <div class="row">
                            <div class="item" @click="onOptionClick(item)"
                                :class="{ 'active': item.id == selectedOption.optionId }"
                                v-for="item in matchOptions.filter(o => o.type == 'WINNER')">
                                <span class="num" v-text="item.optionName" :key="item.id"></span>
                            </div>
                        </div>
                    </div>
                    <div class="betting-box">
                        <h3 class="title">1st or 2nd</h3>
                        <div class="row">
                            <div class="item" @click="onOptionClick(item)"
                                :class="{ 'active': item.id == selectedOption.optionId }"
                                v-for="item in matchOptions.filter(o => o.type == '1ST_OR_2ND')">
                                <span class="num" v-text="item.optionName" :key="item.id"></span>
                            </div>
                        </div>
                    </div>
                    <div class="betting-box">
                        <h3 class="title">Winner</h3>
                        <div class="row">
                            <div class="item" @click="onOptionClick(item)"
                                :class="{ 'active': item.id == selectedOption.optionId }"
                                v-for="item in matchOptions.filter(o => o.type == 'LOW_HIGH_EVEN_ODD')">
                                <span class="num" v-text="item.optionName" :key="item.id"></span>
                            </div>
                        </div>
                    </div>
                    <div class="betting-box">
                        <h3 class="title">Forecast</h3>
                        <div class="row" v-for="idx in 6">
                            <div class="item" @click="onOptionClick(item)"
                                :class="{ 'active': item.id == selectedOption.optionId }"
                                v-for="item in matchOptions.filter(o => o.type == 'FORECAST').splice((idx - 1) * 5, 5)">
                                <span class="num" v-text="item.optionName" :key="item.id"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-panel">

                </div>
            </div>


        </div>
    </div>
</template>

<script setup lang="ts">
import OrderService from '@/api/order';
import matchOptions, { MatchOption } from '@/constants/matchOptions';
import { useResettableReactive } from '@/hooks/useResttableReactive';


interface StakeOrder {
    branchId: number;
    branchName: string;
    cashier: string;
    round: string;
    matchId: number;
    selections: {
        stake: number;
        payout: number;
        odds: number;
        optionId: number;
        optionName: string;
        type: string;
    }[]
}


const [state, resetState] = useResettableReactive<StakeOrder>({
    branchId: 0,
    branchName: 'TEST',
    cashier: '',
    round: '',
    matchId: 1003,
    selections: []
})

const [selectedOption, resetSelectedOption] = useResettableReactive({
    optionId: 0,
    optionName: '',
    odds: 0,
    stake: 0,
    type: ''
});


const onOptionClick = (e: MatchOption) => {
    selectedOption.optionId = e.id;
    selectedOption.odds = e.odds;
    selectedOption.optionName = e.optionName;
    selectedOption.type = e.type!;
}

const clearSelections = () => {
    state.selections = [];
}

const onChipClick = (value: number) => {
    if (selectedOption.optionId == 0) {
        return;
    }
    selectedOption.stake = value;

    let item = state.selections.find(x => x.optionId == selectedOption.optionId);
    if (item != null) {
        item.stake += value;
        item.payout = item.stake * item.odds;
    } else {
        state.selections.push({
            stake: value,
            optionId: selectedOption.optionId,
            optionName: selectedOption.optionName,
            odds: selectedOption.odds,
            type: selectedOption.type,
            payout: value * selectedOption.odds,
        });
    }
    resetSelectedOption();
}


const submit = () => {
    if (state.selections.length == 0) {
        return;
    }

    OrderService.submit(state).then(res => {
        console.log(res);
    }).catch(err => alert(err));
}




const stake = computed(() => {
    return state.selections.reduce((acc, cur) => acc + cur.stake, 0);
})
const maxPayout = computed(() => {
    return state.selections.reduce((acc, cur) => acc + cur.payout, 0);
})


</script>

<style scoped lang="scss">
.text-right {
    text-align: right
}

.container {
    display: flex;
    padding: 2rem 1rem;
    background-color: #fffefb;
}

.branch {
    h3 {
        color: #313d44;
        font-size: .75rem;
        line-height: 1rem;
        padding: 0.75rem;
        background-color: #f5f4f1;
        border-radius: 1000px;
        font-size: 18px;
    }

    margin-bottom: 10px;
}

.left-panel {
    width: 30%;
    min-width: 285px;
    border-right: 1px solid #cccbc8;
    padding-right: 1rem;
}

.right-panel {
    // flex: 1;
    // background-color: #4A7480;
    // padding: 0 10px;
    padding-left: 1rem;
    max-width: 600px;

}



.betting-table {
    border-radius: 5px;
    border-spacing: 0;
    width: 100%;
    white-space: nowrap;
    display: block;
    color: #3b3c3d;
    overflow: hidden;
    margin-bottom: 10px;

    tr {
        height: 35px;
    }

    td,
    th {
        padding: 0 8px;
    }

    thead {
        background-color: #40517D;
        height: 50px;

        background-image: linear-gradient(to right, #d4eaf7, #b6ccd8);
        // background-color: #D4EAF7;

        th {
            font-weight: bold;
        }
    }

    thead,
    tbody tr,
    tfoot tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        text-align: left;
    }

    tbody {
        display: block;
        height: 330px;
        overflow-y: auto;

        tr {
            border-bottom: 1px solid #f5f4f1;
        }
    }

    tfoot {
        background-image: linear-gradient(to right, #d4eaf7, #b6ccd8);
        height: 70px;
    }
}


.betting-box {
    display: flex;
    flex-direction: column;
    // width: auto;
    padding: 0.5rem 1rem;
    margin-bottom: 10px;
    background-color: #f5f4f1;
    border-radius: 0.75rem;
    --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / .05);
    --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);

    .title {
        margin: 0;
        margin-bottom: 0.5rem;
        color: #1d1c1c;
        font-weight: normal;
    }

    .row {
        display: flex;
        margin-bottom: 8px;
        justify-content: flex-start;
        gap: 20px;
    }

    .item {
        width: 60px;
        border: 1px solid #f5f5f5;
        padding: 8px;
        text-align: center;
        border-radius: 6px;
        color: #1d1c1c;
        background-color: #d4eaf7;
        cursor: pointer;
        user-select: none;

        &.active {
            background-color: #b6ccd8;
        }

        &:hover {
            background-color: #b6ccd8;
        }

        &:last-child {
            margin-right: 0;
        }

        .num {
            font-size: 20px;
            // margin-bottom: 10px;
        }

        .num,
        .odds {
            display: block;
        }
    }
}

.chip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 15px;

    .item {
        width: 60px;
        height: 60px;
        line-height: 60px;
        border: 1px solid #f5f5f5;
        border-radius: 1000px;
        text-align: center;
        background-color: #d4eaf7;
        font-weight: bold;
        cursor: pointer;
        user-select: none;

        &:hover {
            background-color: #b6ccd8;
        }
    }
}

.channel {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 15px;

    margin-bottom: 10px;

    div {
        border: 1px solid #f5f5f5;
        height: 50px;
        line-height: 50px;
        padding: 0 10px;
    }
}

.action-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 15px;
    height: 50px;
    margin-bottom: 10px;

    .btn {
        color: #3b3c3d;
        background-color: #b6ccd8;
        font-size: 1.2rem;
        width: 30%;
        text-align: center;
        height: 50px;
        line-height: 50px;
        border: none;
        cursor: pointer;
    }

    .btn-danger {
        color: #fff;
        background-color: #E92845
    }

    .btn-primary {
        color: #fff;
        background-color: #D86260;
    }
}

.tabs {
    display: flex;
    flex-direction: column;
    border: 1px solid #cccbc8;
    border-radius: 5px;
    .tab-header {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        gap: 20px;

        .tab-item {
            padding: 1rem;
        }
    }

    .tab-panel {
        // padding: 20px;
    }
}
</style>