export default function useScanListener(callback: (input: string) => void) {
    let lastTime = 0;
    let buffer = '';

    const handler = (event: KeyboardEvent) => {
        const now = new Date().getTime();

        if (now - lastTime > 50) {
            buffer = ''; // 时间间隔大于50ms，清空缓存
        }

        // console.log(event);

        // 排除特殊按键
        const specialKeys = ['Shift', 'Control', 'Alt', 'Meta', 'CapsLock', 'Escape', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
        if (specialKeys.includes(event.key)) {
            return;
        }

        if (event.key === 'Enter' || event.key === 'Tab') {
            console.log(buffer);
            if (buffer.length > 9) {
                callback(buffer); // 触发回调
            }
            buffer = ''; // 清空缓存
        } else {
            buffer += event.key; // 添加字符到缓存
        }
        lastTime = now;
    };

    onMounted(() => {
        window.addEventListener('keydown', handler);
    });

    onUnmounted(() => {
        window.removeEventListener('keydown', handler);
    });
}