<template>
    <div class="page-body flex-row">
        <div class="left-panel">
            <ticket :matchId="matchId" :items="state.items" :disabled="!isValidDigitCount"
                :selected-option="selectedOption" @clear="clearSelections" :digits="selectedDigits">
            </ticket>
        </div>
        <div class="right-panel">
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Number</h3>
                    <div class="panel-tools pull-right">
                        <span class="selected-number" v-if="selectedDigits.length > 0">Selected Number：<span>{{
                            selectedDigits.length }}</span></span>
                        <el-button type="danger" size="small" @click="clearSelections"
                            :disabled="selectedDigits.length == 0 && selectedOption.optionId == 0"
                            plain>Clear</el-button>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="options color-numbers">
                        <div class="item" @click="onNumberClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'NUMBER')" v-text="item.optionName"
                            :key="key" :class="[item.style, { 'selected': isDigitSelected(item) }]"></div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-header">
                    <h3 class="panel-title">Color</h3>
                </div>
                <div class="panel-body">
                    <div class="options">
                        <div class="item" @click="onOptionClick(item)"
                            v-for="(item, key) in matchOptions.filter(o => o.type == 'COLOR')" v-text="item.optionName"
                            :key="key" :class="[item.style, { 'selected': selectedOption.optionId === item.id }]"></div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>



<script setup lang="ts">
import matchOptions from './option';

import Ticket from '../../components/ticket.vue';

import { useResettableReactive } from '@/hooks/useResettableReactive';
import { MatchOption, StakeOrder } from '@/typings/typings';
import { EMatch } from '@/typings/enum';


const matchId = ref(EMatch.ColorLucky);

const [state] = useResettableReactive<StakeOrder>({
    items: []
})

const [selectedOption, resetSelectedOption] = useResettableReactive({
    optionId: 0,
    optionName: '',
    odds: 0,
    stake: 0,
    type: ''
});




// 存储选择的数字
const selectedDigits = ref<MatchOption[]>([]);

// 点击数字按钮
const onNumberClick = (item: MatchOption) => {
    // 检查是否已经选择了该数字，如果已选择则移除（取消选中）
    if (isDigitSelected(item)) {
        const index = selectedDigits.value.indexOf(item);
        if (index !== -1) {
            selectedDigits.value.splice(index, 1);
        }
        return;
    }

    // 如果已经有8位数字，则不再添加
    if (selectedDigits.value.length >= 8) return;

    // 添加数字到选中列表
    selectedDigits.value.push(item);
    // 清除选中的颜色
    resetSelectedOption();
};

//清除选中的数字和选项
const clearSelections = () => {
    clearSelectedDigits();
    resetSelectedOption();
};


// 判断当前数字数量是否有效（3-8位）
const isValidDigitCount = computed(() => {
    const valid = selectedDigits.value.length >= 3 && selectedDigits.value.length <= 8;
    // 当数字数量有效时，自动更新选项
    if (valid) {
        return false;
    }

    if (selectedOption.optionId !== 0) {
        return false;
    }

    return true;
});

// 检查数字是否已被选择
const isDigitSelected = (option: MatchOption) => {
    return selectedDigits.value.includes(option);
};

// 清空所有选中的数字
const clearSelectedDigits = () => {
    selectedDigits.value = [];
};

const onOptionClick = (e: MatchOption) => {
    clearSelectedDigits();
    if (selectedOption.optionId === e.id) {
        resetSelectedOption();
        return;
    }
    selectedOption.optionId = e.id;
    selectedOption.odds = e.odds;
    selectedOption.optionName = e.optionName;
    selectedOption.type = e.type!;
};

</script>


<style scoped lang="scss">
.selected-number {
    margin-right: 20px;

    span {
        font-weight: bold;
    }
}

.color-numbers {
    gap: 10px;
    flex-wrap: wrap;
    width: 550px;

    .item {
        margin-right: 0;
        margin-bottom: 1px;
    }
}

.item {
    &.blue {
        background-color: #00B0F0;
    }

    &.orange {
        background-color: #FFC000;
    }

    &.purple {
        background-color: #800080;
    }

    &.green {
        background-color: #008000;
    }
}

/* 动态提示样式 */
.dynamic-hint {
    margin-bottom: 15px;
    text-align: center;
}

.hint-text {
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.hint-text.warning {
    color: #e6a23c;
    background-color: #fdf6ec;
}

.hint-text.success {
    color: #67c23a;
    background-color: #f0f9eb;
}

.hint-arrow {
    font-style: normal;
    font-size: 16px;
    font-weight: bold;
    animation: bounce 1s infinite;
    display: inline-block;
    margin-left: 5px;
}

@keyframes bounce {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(5px);
    }
}
</style>
