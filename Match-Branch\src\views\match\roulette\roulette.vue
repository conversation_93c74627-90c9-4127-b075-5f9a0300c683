<template>
    <div class="page-body flex-row">
        <!-- 新增round选择组件 -->
        <div class="left-panel">
            <navbar></navbar>
            <round></round>
            <ticket :matchId="matchId" :items="state.items" :selected-option="selectedOption"
                @clear="resetSelectedOption"></ticket>
        </div>
        <div class="right-panel">
            <div class="flex-row container">
                <div class="box number-box">
                    <button class="option-btn number" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName != '0').slice(0, 12)">
                        {{ item.optionName }}
                    </button>

                    <button class="option-btn dozens" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'Dozens').slice(0, 1)">
                        <span>1st dozen</span>
                        {{ item.optionName }}
                    </button>
                </div>
                <div class="box number-box">
                    <button class="option-btn number" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName != '0').slice(12, 24)">
                        {{ item.optionName }}
                    </button>
                    <button class="option-btn dozens" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'Dozens').slice(1, 2)">
                        <span>2nd dozen</span>
                        {{ item.optionName }}
                    </button>
                </div>
                <div class="box number-box">
                    <button class="option-btn number" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName != '0').slice(24, 36)">
                        {{ item.optionName }}
                    </button>
                    <button class="option-btn dozens" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'Dozens').slice(2, 3)">
                        <span>3rd dozen</span>
                        {{ item.optionName }}
                    </button>
                </div>
            </div>
            <div class="flex-row container">
                <div class="box number-box">
                    <button class="option-btn number" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'ExactNumber' && o.optionName == '0')">
                        {{ item.optionName }}
                    </button>
                </div>
                <div class="box sector-box">
                    <button class="option-btn sector" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOption.optionId === item.id }]" :key="item.id"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type == 'Sector')">
                        <span>Sector</span>
                        {{ item.optionName }}
                    </button>
                </div>
            </div>
            <div class="flex-row container">
                <div class="box other-box">
                    <button class="option-btn other" :data-tooltip="'x' + item.odds"
                        :class="[item.style, { 'selected': selectedOption.optionId === item.id }]"
                        @click="onOptionClick(item)"
                        v-for="(item, key) in matchOptions.filter(o => o.type?.startsWith('Other'))">
                        {{ item.optionName }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

import matchOptions from './option';

import Ticket from '../../components/ticket.vue';
import Navbar from '../../components/navbar.vue';
import Round from '../../components/round.vue';

import { useResettableReactive } from '@/hooks/useResettableReactive';

import { EMatch } from '@/typings/enum';
import { MatchOption, StakeOrder } from '@/typings/typings';




const matchId = ref(EMatch.LuckyRoulette);

const [state] = useResettableReactive<StakeOrder>({
    items: []
})

const [selectedOption, resetSelectedOption] = useResettableReactive({
    optionId: 0,
    optionName: '',
    odds: 0,
    stake: 0,
    type: ''
});


const onOptionClick = (e: MatchOption) => {
    // 如果点击的是已选中的选项，则取消选中
    if (selectedOption.optionId === e.id) {
        resetSelectedOption();
        return;
    }

    // 否则选中新选项
    selectedOption.optionId = e.id;
    selectedOption.odds = e.odds;
    selectedOption.optionName = e.optionName;
    selectedOption.type = e.type!;
};



</script>

<style scoped lang="scss">
.container {
    gap: 5px;
    margin-bottom: 5px;

    [data-tooltip]:before {
        font-size: 26px;
    }

    .number-box {
        gap: 8px 15px;
        display: flex;
        flex-wrap: wrap;
        width: 33.333%;
        flex: 1;
        justify-content: center;
        align-items: center;

        .option-btn {
            flex: 0 0 calc(33.333% - 13px); // 每行3个按钮，减去gap
        }
    }

    .other-box {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        gap: 8px 15px;
    }

    .sector-box {
        display: flex;
        width: 66.66666%;
        gap: 8px 15px;
        // justify-content: center;
        align-items: center;

    }
}


.option-btn {
    border-radius: 8px;
    width: 80px;
    height: 60px;
    color: #fff;
    font-weight: bold;
    // 添加文字描边
    // -webkit-text-stroke: 1px #000;
    // -webkit-text-stroke-color: #000;
    // -webkit-text-stroke-width: 1px;
    user-select: none;
    padding: 0;
    font-size: 24px;

    &.other {
        font-size: 20px;
    }

    &.number {
        font-size: 36px;
    }

    &.green {
        background: linear-gradient(180deg,
                #DFFE8A 0%,
                /* 上半部分起始色 */
                #DCF95F 25%,
                /* 上半部分结束色 */
                #D7FF76 50%,
                /* 中间分界线 */
                #47890c 53%,
                /* 下半部分起始色 */
                #47890c 100%
                /* 下半部分结束色 */
            );
    }

    &.dozens,
    &.sector {
        font-size: 20px;

        span {
            font-size: 14px;
            font-weight: bold;
            white-space: nowrap;
            display: block;
        }
    }

    &.red {
        // 上下两段不同渐变
        background: linear-gradient(180deg,
                #ED5125 0%,
                /* 上半部分起始色 */
                #CF2F1A 25%,
                /* 上半部分结束色 */
                #A72C1C 50%,
                /* 中间分界线 */
                #790700 53%,
                /* 下半部分起始色 */
                #883225 100%
                /* 下半部分结束色 */
            );
    }

    &.gray {
        color: #000;
        -webkit-text-stroke: 1px #fff;
        -webkit-text-stroke-color: #fff;
        -webkit-text-stroke-width: 1px;
        background: linear-gradient(180deg,
                #e7ebef 0%,
                /* 上半部分起始色 */
                #e0e8eb 25%,
                /* 上半部分结束色 */
                #e1e3e8 50%,
                /* 中间分界线 */
                #c0c1da 53%,
                /* 下半部分起始色 */
                #c3cfe5 100%
                /* 下半部分结束色 */
            );

    }

    &.black,
    &.dozens {
        background: linear-gradient(180deg,
                #5d5d55 0%,
                /* 上半部分起始色 */
                #42413c 25%,
                /* 上半部分结束色 */
                #383732 50%,
                /* 中间分界线 */
                #1e1b16 53%,
                /* 下半部分起始色 */
                #1a1711 100%
                /* 下半部分结束色 */
            );
    }


}

[data-tooltip] {
    position: relative;
}

[data-tooltip]:before {
    content: attr(data-tooltip);
    position: absolute;
    color: #fff;
    padding: 5px;
    border-radius: 3px;
    font-size: 26px;
    right: -25px;
    top: 0;
    z-index: 1;
    opacity: 0;
    transform: translateY(5px);
    visibility: hidden;

    // 添加过渡动画
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
}

[data-tooltip]:hover:before {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
}
</style>
