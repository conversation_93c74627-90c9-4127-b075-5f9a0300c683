<template>
    <div class="wrapper">
        <div class="select flex-row align-center justify-between">
            <div class="current">
                Game Select (SN-6680)
            </div>
            <div class="bet-limit text-right">
                <div>Min bet:100</div>
                <div>Max bet: {{ formatNumber(50000) }}</div>
            </div>
        </div>
        <div class="rounds-container">
            <div v-for="(item, idx) in filteredRounds" :key="item.id"
                :class="['round-item', { active: selectedRound === item.id }]" @click="selectRound(item)">
                <div class="sn"> SN - {{ item.round }}</div>
                <div class="time">
                    Locked in
                    < 1.33 min <!-- <span>{{ formatTime(item.endTime).hours }}</span> hour
                        <span>{{ formatTime(item.endTime).minutes }}</span> min
                        <span>{{ formatTime(item.endTime).seconds }}</span> second -->
                </div>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>
import MatchService from '@/api/match';
import { RoundModel } from '@/api/typings';
import { formatNumber } from '@/utils';
import { ref, onMounted, computed, watchEffect } from 'vue';

const selectedRound = ref<string>("");

const selectRound = (item: RoundModel) => {
    selectedRound.value = item.id;
};

const rounds = ref<RoundModel[]>([]);

const getRounds = async () => {
    MatchService.getRounds({ matchId: 1000 }).then(res => {
        rounds.value = res.result.slice(0,100);
    });
};
onMounted(() => {
    getRounds();
});

const formatTime = (endTime: string): { hours: number, minutes: number, seconds: number } => {
    const end = new Date(endTime).getTime();
    const now = new Date().getTime();
    let diffInSeconds = Math.ceil((end - now) / 1000);

    const hours = Math.floor(diffInSeconds / 3600);
    diffInSeconds %= 3600;
    const minutes = Math.floor(diffInSeconds / 60);
    const seconds = diffInSeconds % 60;

    return { hours, minutes, seconds };
};

const filteredRounds = computed(() => {
    const now = new Date().getTime();
    return rounds.value.filter(item => new Date(item.endTime).getTime() > now);
});

// 更新倒计时，确保时间自动刷新
const updateTime = () => {
    // 触发响应式更新
};

// 每秒更新一次时间
setInterval(updateTime, 1000);

</script>


<style scoped lang="scss">
.wrapper {
    background-color: #29220F;
    color: #fff;
    min-height: 130px;
    padding-bottom: 0;
    border-radius: 10px;
    box-sizing: border-box;
    width: 400px;
    margin-bottom: 10px;
    overflow: hidden;
    .select{
        padding: 5px;
        .current{
            font-size: 16px;
            font-weight: bold;
        }
        .bet-limit{
            font-size: 14px;
        }
    }
}

.rounds-container {
    display: flex;
    overflow-x: auto;
    gap: 5px;
    padding: 0 5px;
    &::-webkit-scrollbar {
        // height: 3px;
        background-color: #409EFF;
    }
}

.round-item {
    flex-shrink: 0;
    width: 80px;
    height: 70px;
    text-align: center;
    text-align: left;
    border-radius: 5px;
    background-color: #e4e8f0;
    color: #333;
    font-weight: 500;
    cursor: pointer;
    box-sizing: border-box;
    transition: all 0.3s ease;
    padding: 5px;

    user-select: none;

    .sn {
        font-size: 16px;
        font-weight: bold;
        white-space: nowrap;
    }

    .time {
        font-size: 14px;
    }

    &.active {
        background-color: #409EFF;
        color: white;
    }

    &:hover {
        background-color: #409EFF;
    }
}
</style>